import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
// Corrected import for lovable-tagger to use a named import
import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode, command }) => {
  // Check for subpath deployment environment variable
  const isSubpathDeployment = process.env.VITE_BASE_PATH === '/infinite_banking/';
  const base = isSubpathDeployment ? '/infinite_banking/' : '/';

  console.log(`🚀 Vite Config - Mode: ${mode}, Command: ${command}, Base: ${base}`);

  return {
    base: base,
    build: {
      outDir: "dist",
      rollupOptions: {
        input: "index.html",
      },
    },
    server: {
      host: "::",
      port: 8080, // Your preferred local dev port
    },
    plugins: [
      react(),
      // Conditionally add componentTagger for development
      (mode === 'development' && componentTagger ? componentTagger() : null),
    ].filter(<PERSON><PERSON><PERSON>),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
  };
});
