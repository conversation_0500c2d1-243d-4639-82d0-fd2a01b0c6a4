
import React from 'react';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import SocialProofSection from '@/components/SocialProofSection';
import RetirementPlansSection from '@/components/RetirementPlansSection';
import FinancialTransformationSection from '@/components/FinancialTransformationSection';
import AboutJasonSection from '@/components/AboutJasonSection';
import VortexBankingSection from '@/components/VortexBankingSection';
import HowItWorksSection from '@/components/HowItWorksSection';
import ProofSection from '@/components/ProofSection';
import InvestmentSection from '@/components/InvestmentSection';
import ObjectionsSection from '@/components/ObjectionsSection';
import GuaranteeSection from '@/components/GuaranteeSection';
import UrgencySection from '@/components/UrgencySection';
import Footer from '@/components/Footer';

const Index = () => {
  // Fix for the highlighted price in the content
  const handleClick = () => {
    console.log('CTA clicked - would redirect to booking page');
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main>
        <HeroSection />
        <SocialProofSection />
        <RetirementPlansSection />
        <FinancialTransformationSection />
        <AboutJasonSection />
        <VortexBankingSection />
        <HowItWorksSection />
        <ProofSection />
        <InvestmentSection />
        <ObjectionsSection />
        <GuaranteeSection />
        <UrgencySection />
      </main>
      
      <Footer />
    </div>
  );
};

export default Index;
