
import React, { useState } from 'react';
import PrivacyPolicy from './PrivacyPolicy';
import TermsOfService from './TermsOfService';

const Footer = () => {
  const [privacyOpen, setPrivacyOpen] = useState(false);
  const [termsOpen, setTermsOpen] = useState(false);
  
  return <footer className="bg-vortex-dark text-white py-8">
      <div className="max-w-7xl mx-auto px-6 text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <span className="bg-white text-vortex font-bold text-2xl px-3 py-1 rounded">V</span>
          <span className="font-playfair font-bold text-xl">Vortex Banker</span>
        </div>
        
        <div className="mb-6">
          <p className="text-sm opacity-70">
            © {new Date().getFullYear()} LinkTech Foundation. All rights reserved.
          </p>
        </div>
        
        <div className="flex justify-center gap-6 text-sm opacity-70">
          <button onClick={() => setPrivacyOpen(true)} className="hover:text-vortex-light">
            Privacy Policy
          </button>
          <button onClick={() => setTermsOpen(true)} className="hover:text-vortex-light">
            Terms of Service
          </button>
        </div>

        <p className="text-xs opacity-50 mt-8 max-w-2xl mx-auto">
          This website is for informational purposes only and does not constitute financial advice. 
          Individual results may vary. Please consult with qualified professionals for advice specific to your situation.
        </p>
      </div>
      
      <PrivacyPolicy open={privacyOpen} onOpenChange={setPrivacyOpen} />
      <TermsOfService open={termsOpen} onOpenChange={setTermsOpen} />
    </footer>;
};

export default Footer;
