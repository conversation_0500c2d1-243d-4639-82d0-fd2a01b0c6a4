// Deployment configuration helper
// This script helps switch between root and subpath deployment configurations

const fs = require('fs');
const path = require('path');

const CONFIGS = {
  root: {
    base: '/',
    description: 'Root deployment (e.g., https://infinite-banking.vercel.app/)'
  },
  subpath: {
    base: '/infinite_banking/',
    description: 'Subpath deployment (e.g., https://www.linktechfoundation.org/infinite_banking)'
  }
};

function updateViteConfig(deploymentType) {
  const configPath = path.join(__dirname, 'vite.config.ts');
  let content = fs.readFileSync(configPath, 'utf8');
  
  const config = CONFIGS[deploymentType];
  if (!config) {
    console.error('❌ Invalid deployment type. Use "root" or "subpath"');
    process.exit(1);
  }

  // Update the base path logic
  const newLogic = deploymentType === 'subpath' 
    ? `const base = '/infinite_banking/';`
    : `const base = '/';`;

  // Replace the base path determination logic
  content = content.replace(
    /const isSubpathDeployment = .*?\n\s*const base = .*?;/s,
    `const base = '${config.base}';`
  );

  fs.writeFileSync(configPath, content);
  
  console.log(`✅ Updated vite.config.ts for ${config.description}`);
  console.log(`📁 Base path set to: ${config.base}`);
}

// Get deployment type from command line argument
const deploymentType = process.argv[2];

if (!deploymentType) {
  console.log('🚀 Deployment Configuration Helper\n');
  console.log('Usage: node deploy-config.js <type>\n');
  console.log('Available types:');
  Object.entries(CONFIGS).forEach(([key, config]) => {
    console.log(`  ${key}: ${config.description}`);
  });
  process.exit(0);
}

updateViteConfig(deploymentType);
