import React from 'react';
import StrikethroughPrice from './StrikethroughPrice';
import CTAButton from './CTAButton';
import { ShieldCheck, Clock, Star } from 'lucide-react';

const HeroSection = () => {
  return (
    <section className="hero-gradient text-white py-16 lg:py-24 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div className="animate-fade-in">
            <h1 className="font-montserrat text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              The $100 Million Family Office Strategy Now Available To Everyday Americans
            </h1>
            <p className="mb-6 text-lg opacity-90 font-roboto">
              Discover Vortex Banking: The enhanced Infinite Banking Concept that Wall Street elites, major corporations, and ultra-wealthy families have used for generations – now accessible to hardworking entrepreneurs and families for the first time ever
            </p>
            
            <div className="p-4 bg-white/10 backdrop-blur-sm rounded-lg mb-8 border border-white/20">
              <p className="font-medium text-lg">
                "JPMorgan Chase has $12.6 billion in Bank-Owned Life Insurance... Walt Disney used it to fund Disneyland... and the wealthiest 1% have been using this strategy for decades through their private Family Offices. Now it's YOUR turn."
              </p>
            </div>

            {/* Trust indicators */}
            <div className="flex flex-wrap gap-4 mb-6">
              <div className="flex items-center bg-white/10 rounded-full py-1 px-3">
                <ShieldCheck className="w-4 h-4 mr-2 text-vortex-green" />
                <span className="text-sm">20+ Years Experience</span>
              </div>
              <div className="flex items-center bg-white/10 rounded-full py-1 px-3">
                <Star className="w-4 h-4 mr-2 text-vortex-orange" />
                <span className="text-sm">500+ Families Helped</span>
              </div>
              <div className="flex items-center bg-white/10 rounded-full py-1 px-3">
                <Clock className="w-4 h-4 mr-2 text-vortex-pink" />
                <span className="text-sm">Limited Availability</span>
              </div>
            </div>
            
            {/* Scarcity notice */}
            <div className="bg-vortex-red/20 border border-vortex-red/30 text-white p-3 rounded-md mb-6">
              <p className="text-sm font-medium flex items-center">
                <Clock className="w-4 h-4 mr-2 text-vortex-red" />
                Only the next 10 appointments will be $25, after that the price returns to <StrikethroughPrice originalPrice="$250" currentPrice="" />
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <CTAButton 
                text={
                  <span>Book Your Vortex Strategy Call <span className="whitespace-nowrap">($25 - <StrikethroughPrice originalPrice="$250" currentPrice="" />)</span></span>
                } 
                className="flex-1"
              />
              <CTAButton 
                text="Try Our Elite Wealth Calculator" 
                isPrimary={false}
                className="flex-1"
              />
            </div>
          </div>
          
          <div className="animate-fade-in hidden md:block">
            <div className="bg-gradient-to-br from-vortex/20 to-vortex-secondary/20 p-6 rounded-xl border border-white/10">
              <div className="bg-white/10 backdrop-blur rounded-lg p-5 mb-5 animate-pulse-subtle">
                <h3 className="font-montserrat font-bold text-xl mb-2">Banking Giants Using This Strategy:</h3>
                <ul className="space-y-3 font-roboto">
                  <li className="flex items-center">
                    <span className="inline-block w-3 h-3 bg-vortex rounded-full mr-3"></span>
                    <span className="text-white font-medium">JPMorgan Chase:</span>
                    {'\u00A0'} {/* Non-breaking space */}
                    <span className="font-bold text-green-400">$12.6 BILLION</span>
                    {'\u00A0'} {/* Non-breaking space */}
                    <span>in BOLI assets</span> {/* Wrapped in a span */}
                  </li>
                  <li className="flex items-center">
                    <span className="inline-block w-3 h-3 bg-vortex-orange rounded-full mr-3"></span>
                    <span className="text-white font-medium">Bank of America:</span>
                    {'\u00A0'} {/* Non-breaking space */}
                    <span className="font-bold text-green-400">$9.3 BILLION</span>
                    {'\u00A0'} {/* Non-breaking space */}
                    <span>in BOLI assets</span> {/* Wrapped in a span */}
                  </li>
                  <li className="flex items-center">
                    <span className="inline-block w-3 h-3 bg-vortex-pink rounded-full mr-3"></span>
                    <span className="text-white font-medium">Wells Fargo:</span>
                    {'\u00A0'} {/* Non-breaking space */}
                    <span className="font-bold text-green-400">$8.5 BILLION</span>
                    {'\u00A0'} {/* Non-breaking space */}
                    <span>in BOLI assets</span> {/* Wrapped in a span */}
                  </li>
                  <li className="flex items-center">
                    <span className="inline-block w-3 h-3 bg-vortex-green rounded-full mr-3"></span>
                    <span className="text-white font-medium">Citigroup:</span>
                    {'\u00A0'} {/* Non-breaking space */}
                    <span className="font-bold text-green-400">$7.2 BILLION</span>
                    {'\u00A0'} {/* Non-breaking space */}
                    <span>in BOLI assets</span> {/* Wrapped in a span */}
                  </li>
                </ul>
              </div>
              <div className="text-center">
                <p className="font-bold">The banking elite aren't just using this strategy –</p>
                <p className="text-xl">They're betting <span className="text-vortex font-bold">BILLIONS</span> on it!</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="absolute -bottom-16 -left-16 w-64 h-64 bg-vortex/10 rounded-full blur-3xl"></div>
      <div className="absolute -top-20 -right-20 w-72 h-72 bg-vortex-tertiary/10 rounded-full blur-3xl"></div>
    </section>
  );
};

export default HeroSection;