import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
// Assuming lovable-tagger might still be used, adjust if not.
// If you were using the named import, it would be:
// import { componentTagger } from "lovable-tagger"; 
import componentTagger from "lovable-tagger"; // Or however it was in your original working version

export default defineConfig(({ mode }) => {
  // For root deployment (e.g., https://your-project.vercel.app/), base should be '/'
  const base = '/'; 

  return {
    base: base,
    build: {
      outDir: "dist",
      rollupOptions: {
        input: "index.html", 
      },
    },
    server: {
      host: "::", 
      port: 8080, // Your preferred local dev port
    },
    plugins: [
      react(),
      // Conditionally add componentTagger for development if it was part of your setup
      (mode === 'development' && componentTagger ? componentTagger() : null),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
  };
});
