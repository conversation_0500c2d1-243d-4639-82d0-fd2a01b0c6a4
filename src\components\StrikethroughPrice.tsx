
import React from 'react';

interface StrikethroughPriceProps {
  originalPrice: string;
  currentPrice?: string;
}

const StrikethroughPrice = ({ originalPrice, currentPrice }: StrikethroughPriceProps) => {
  return (
    <span className="whitespace-nowrap">
      <span className="text-vortex-red font-medium line-through relative">
        {originalPrice}
        <span className="absolute left-0 top-1/2 w-full h-[3px] bg-vortex-red transform -rotate-6"></span>
      </span>
      {currentPrice && <span className="ml-2 font-bold">{currentPrice}</span>}
    </span>
  );
};

export default StrikethroughPrice;
