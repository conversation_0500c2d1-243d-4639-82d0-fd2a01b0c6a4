
import React from 'react';

const ObjectionCard = ({ question, answer }: { question: string, answer: string }) => (
  <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
    <h3 className="text-lg font-bold mb-3 text-vortex-dark">{question}</h3>
    <p className="text-gray-700">{answer}</p>
  </div>
);

const ObjectionsSection = () => {
  const objections = [
    {
      question: "Is this really available to regular people like me?",
      answer: "For decades, this strategy was reserved for families with $100+ million net worth and their private Family Offices. What makes our Vortex Banking system revolutionary is that we've made it accessible to everyday American families. If you have the drive to build wealth, you deserve the same advantages as the ultra-wealthy."
    },
    {
      question: "Is this just whole life insurance with a fancy name?",
      answer: "Calling Vortex Banking 'just insurance' is like calling Amazon 'just a website.' While we utilize specially-structured whole life insurance as the financial vehicle, our proprietary Contract Law Trust Strategy and MEC optimization create a comprehensive wealth-building system that even traditional IBC practitioners don't understand or utilize."
    },
    {
      question: "I've heard policies are rigid and inflexible.",
      answer: "Traditional IBC accounts ARE rigid. If you commit to $24K annually and suddenly sell a property or get a bonus, you're stuck. Our Vortex Banking system gives you a flexible warehouse with up to $200K additional contribution capacity anytime – something traditional advisors simply can't offer."
    },
    {
      question: "What makes your strategy different from regular IBC?",
      answer: "Three critical differences: 1) We've removed the contribution limits that plague traditional IBC, 2) We strategically MEC the policies from day one for better growth, and 3) We're the ONLY company offering a structure that allows pre-tax dollars to fund your policy. This means your money is NEVER taxed – not when it goes in, not while it grows, and not when it comes out."
    }
  ];

  return (
    <section className="section bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-vortex-dark">
            Common Questions About Vortex Banking
          </h2>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {objections.map((objection, index) => (
            <ObjectionCard 
              key={index} 
              question={objection.question} 
              answer={objection.answer} 
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ObjectionsSection;
