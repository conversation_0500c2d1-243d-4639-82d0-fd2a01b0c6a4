
import React from 'react';
import CTAButton from './CTAButton';

const VortexBankingSection = () => {
  const coreBenefits = [
    {
      title: "Pre-Tax Funding",
      description: "Use dollars the government never touches – money that would otherwise be lost to taxes forever"
    },
    {
      title: "Unlimited Flexibility",
      description: "While traditional IBC limits your yearly contributions, Vortex Banking allows additional deposits up to $200K anytime throughout the year"
    },
    {
      title: "Double Growth Miracle",
      description: "The ONLY strategy in existence that lets your money grow in two places simultaneously – a financial impossibility made reality"
    },
    {
      title: "Total Control",
      description: "Create your own private banking system that's immune to market crashes, bank failures, and government overreach"
    }
  ];

  const bankingGiants = [
    { name: "Citigroup", amount: "$4.8 BILLION in BOLI assets" },
    { name: "U.S. Bancorp", amount: "$3.1 BILLION in BOLI assets" },
    { name: "Truist Financial", amount: "$2.7 BILLION in BOLI assets" },
    { name: "PNC Financial", amount: "$2.4 BILLION in BOLI assets" },
    { name: "Goldman Sachs", amount: "$1.9 BILLION in BOLI assets" },
    { name: "Morgan Stanley", amount: "$1.5 BILLION in BOLI assets" }
  ];

  const corporateExamples = [
    { name: "AT&T", amount: "$600 MILLION in COLI holdings" },
    { name: "Verizon", amount: "$500 MILLION in COLI holdings" },
    { name: "FedEx", amount: "$250 MILLION in COLI holdings" }
  ];

  return (
    <section className="section bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-vortex-dark">
            Vortex Banking: The Financial Strategy They Don't Want You To Know About
          </h2>
          <p className="text-xl text-gray-700">
            The ONLY wealth-building system that lets your money grow in <span className="text-vortex-green font-bold">TWO</span> places at once
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {coreBenefits.map((benefit, index) => (
            <div 
              key={index} 
              className="bg-white p-6 rounded-xl shadow-md border border-gray-100 transition-all duration-300 hover:shadow-lg"
            >
              <h3 className={`font-bold text-lg mb-3 ${index % 2 === 0 ? 'text-vortex' : 'text-vortex-green'}`}>{benefit.title}</h3>
              <p className="text-gray-700">{benefit.description}</p>
            </div>
          ))}
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-vortex-dark text-white p-6 rounded-xl">
            <h3 className="font-bold text-xl mb-4 text-vortex-green">Banking Giants With Billions</h3>
            <div className="space-y-4">
              {bankingGiants.map((bank, index) => (
                <div key={index} className="bg-white/10 p-4 rounded-lg">
                  <p className="font-bold">{bank.name}</p>
                  <p className="text-vortex-light">{bank.amount}</p>
                </div>
              ))}
            </div>
          </div>

          <div className="md:col-span-2 bg-white p-6 rounded-xl shadow-lg border border-gray-100">
            <h3 className="text-vortex-dark font-bold text-xl mb-4">Example That Changes Everything</h3>
            <div className="prose text-gray-700 space-y-4">
              <p>Let me show you the <span className="text-vortex font-bold">MIRACLE</span> of Vortex Banking with a simple example:</p>
              
              <p>Imagine you have $100,000 in your Vortex Banking cash value. An investment opportunity appears – a property you want to fix and flip. You borrow $50,000 from your policy to buy and remodel the house.</p>
              
              <p>Now, how much money is still earning interest and dividends in your policy?</p>
              
              <p className="font-bold">If you said $50,000, you'd be <span className="text-vortex-green">DEAD WRONG</span>.</p>
              
              <p className="bg-vortex-light/20 p-4 rounded-lg font-bold my-6">The correct answer: THE FULL <span className="text-vortex-green">$100,000</span>.</p>
              
              <p>How is this possible? When you take cash value from your policy, you're actually borrowing from the insurance company's general fund – not your policy. Your money <span className="text-vortex-green font-bold">NEVER LEAVES</span>. It continues growing, uninterrupted.</p>
              
              <p className="mt-5">You might think, 'Borrowing sounds expensive...' <span className="text-vortex font-bold">WRONG AGAIN</span>.</p>
              
              <p className="mt-5">You're getting this money for pennies on the dollar. Don't believe me? Just look at the <span className="text-vortex-green font-bold">BILLIONS</span> that JPMorgan, Bank of America, and Wells Fargo have invested in this exact strategy.</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-100 p-8 rounded-xl mb-12">
          <h3 className="text-center text-2xl font-bold text-vortex-dark mb-6">More Companies Using This Strategy</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {corporateExamples.map((company, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm text-center">
                <p className="font-bold">{company.name}</p>
                <p className="text-vortex-green">{company.amount}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="text-center">
          <CTAButton 
            text="Book Your Vortex Strategy Call (Special: $25 - Regular Price: $250)"
            className="mx-auto px-12"
          />
        </div>
      </div>
    </section>
  );
};

export default VortexBankingSection;
