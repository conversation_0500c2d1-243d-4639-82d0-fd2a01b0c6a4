import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

console.log('🚀 main.tsx - Starting React app');
console.log('🚀 main.tsx - import.meta.env.BASE_URL:', import.meta.env.BASE_URL);
console.log('🚀 main.tsx - window.location:', window.location.href);

const rootElement = document.getElementById("root");
if (rootElement) {
  createRoot(rootElement).render(<App />);
  console.log('🚀 main.tsx - React app rendered successfully');
} else {
  console.error('❌ main.tsx - Root element not found!');
}
