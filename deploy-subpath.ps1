# PowerShell script to build for subpath deployment
Write-Host "🚀 Building for subpath deployment (/infinite_banking/)" -ForegroundColor Green

# Set environment variable for this session
$env:VITE_BASE_PATH = "/infinite_banking/"

Write-Host "📁 Base path set to: $env:VITE_BASE_PATH" -ForegroundColor Yellow

# Build the project
Write-Host "🔨 Building project..." -ForegroundColor Blue
npm run build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
    Write-Host "📦 Files are ready in the 'dist' directory" -ForegroundColor Cyan
    Write-Host "🌐 Deploy the 'dist' directory to your subpath hosting" -ForegroundColor Cyan
} else {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    exit 1
}
