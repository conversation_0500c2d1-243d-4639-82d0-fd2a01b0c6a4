
import React from 'react';
import CTAButton from './CTAButton';

const RetirementPlansSection = () => {
  const benefits = [
    "No stomach-churning market volatility",
    "No government restrictions on YOUR money",
    "No surprise tax bills in retirement",
    "No begging banks for loans",
    "No losing sleep over market crashes"
  ];

  const keyFeatures = [
    "Your money grows tax-free and can be accessed tax-free",
    "You get returns equivalent to 7%+ annually WITHOUT market risk",
    "You can use your money anytime without penalties or restrictions",
    "Your account is guaranteed to grow by a larger amount each year",
    "You become your own source of financing - no more banks"
  ];

  return (
    <section className="section bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-vortex-dark">
            Discover The 'Swiss Army Knife' Of Wealth Building That The Father Of The 401(k) Now Uses For His Own Money
          </h2>
        </div>

        <div className="bg-red-50 border-l-4 border-red-500 p-6 mb-12 rounded-r-lg shadow-sm">
          <h3 className="text-xl font-bold text-red-700 mb-2">WARNING:</h3>
          <p className="text-gray-800">
            Your retirement account is likely missing <span className="font-bold">$186,000</span> right now due to hidden fees that are silently devouring your savings. But there's a better way...
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100 animate-fade-in">
            <h3 className="text-xl font-bold mb-4 text-vortex-dark">The Insider Revelation</h3>
            <p className="mb-6 text-gray-700">
              The man who invented the 401(k) now calls it a 'monster' and says it should be 'blown up.' Even more shocking - he's moved the majority of his own wealth into an alternative strategy that:
            </p>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-vortex mr-2">✓</span>
                <span>Has never lost money in 160+ years (even during the Great Depression)</span>
              </li>
              <li className="flex items-start">
                <span className="text-vortex mr-2">✓</span>
                <span>Grows by a larger guaranteed amount every single year</span>
              </li>
              <li className="flex items-start">
                <span className="text-vortex mr-2">✓</span>
                <span>Lets you access your money anytime with no penalties</span>
              </li>
              <li className="flex items-start">
                <span className="text-vortex mr-2">✓</span>
                <span>Provides tax-free retirement income</span>
              </li>
              <li className="flex items-start">
                <span className="text-vortex mr-2">✓</span>
                <span>Gives you guaranteed returns WITHOUT stock market risk</span>
              </li>
            </ul>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100 animate-fade-in">
            <h3 className="text-xl font-bold mb-4 text-vortex-dark">This Isn't Your Typical Investment Strategy</h3>
            <ul className="space-y-3 mb-6">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <span className="inline-block bg-vortex text-white w-6 h-6 rounded-full flex items-center justify-center mr-3 shrink-0">→</span>
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>

            <div className="p-4 bg-gray-100 rounded-lg mb-4">
              <p className="text-gray-800">
                <span className="font-bold">The stark contrast:</span> The average 401(k) holder lost 22.9% in 2022 alone. Meanwhile, people using this strategy saw their wealth continue growing - guaranteed.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-vortex-dark text-white p-8 rounded-xl shadow-lg">
          <h3 className="text-2xl font-bold mb-6">What Makes This Different:</h3>
          <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-6">
            {keyFeatures.map((feature, index) => (
              <div key={index} className="bg-white/10 p-4 rounded-lg">
                <p>{feature}</p>
              </div>
            ))}
          </div>

          <div className="mt-8 text-center">
            <CTAButton 
              text="Book Your Vortex Strategy Call (Special: $25 - Regular Price: $250)"
              className="w-full max-w-lg mx-auto"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default RetirementPlansSection;
