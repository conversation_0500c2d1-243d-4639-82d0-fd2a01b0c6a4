@echo off
echo 🚀 Building for subpath deployment (/infinite_banking/)

REM Set environment variable for this session
set VITE_BASE_PATH=/infinite_banking/

echo 📁 Base path set to: %VITE_BASE_PATH%

echo 🔨 Building project...
npm run build

if %errorlevel% equ 0 (
    echo ✅ Build completed successfully!
    echo 📦 Files are ready in the 'dist' directory
    echo 🌐 Deploy the 'dist' directory to your subpath hosting
) else (
    echo ❌ Build failed!
    exit /b 1
)
