# Deployment Guide

This project supports two deployment scenarios:

1. **Root Deployment**: `https://infinite-banking.vercel.app/`
2. **Subpath Deployment**: `https://www.linktechfoundation.org/infinite_banking`

## Quick Start

### For Root Deployment (Default)
```bash
npm run build
```

### For Subpath Deployment
```bash
# Windows PowerShell
$env:VITE_BASE_PATH="/infinite_banking/"; npm run build

# Windows Command Prompt
set VITE_BASE_PATH=/infinite_banking/ && npm run build

# Or use the provided scripts
.\deploy-subpath.ps1  # PowerShell
deploy-subpath.bat    # Command Prompt
```

## How It Works

### Vite Configuration
The `vite.config.ts` automatically detects the deployment type based on the `VITE_BASE_PATH` environment variable:

- **Root**: `base: '/'` (default)
- **Subpath**: `base: '/infinite_banking/'` (when `VITE_BASE_PATH=/infinite_banking/`)

### React Router Configuration
The `App.tsx` uses `import.meta.env.BASE_URL` which automatically matches Vite's base configuration:

```tsx
<BrowserRouter basename={import.meta.env.BASE_URL}>
```

### Vercel Configuration
The `vercel.json` handles SPA routing for both deployment types:

```json
{
  "rewrites": [
    {
      "source": "/infinite_banking/(.*)",
      "destination": "/infinite_banking/index.html"
    },
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

## Deployment Steps

### 1. Root Deployment to Vercel
```bash
# Build for root deployment
npm run build

# Deploy to Vercel (if using Vercel CLI)
vercel --prod
```

### 2. Subpath Deployment
```bash
# Build for subpath deployment
$env:VITE_BASE_PATH="/infinite_banking/"; npm run build

# The dist/ folder now contains files configured for subpath deployment
# Upload the contents of dist/ to your hosting provider under the /infinite_banking/ path
```

## Verification

After deployment, check these items:

### ✅ Assets Loading Correctly
- CSS and JS files should load without 404 errors
- Check browser DevTools Network tab

### ✅ React Router Working
- Navigate to different routes (if you have them)
- Refresh the page on a route - should not show 404

### ✅ Console Logs
Look for these logs in the browser console:
```
🚀 App.tsx - basename: /infinite_banking/
🚀 App.tsx - import.meta.env.BASE_URL: /infinite_banking/
```

## Troubleshooting

### Issue: Blank White Page
**Cause**: Usually React Router basename mismatch

**Solution**: 
1. Check browser console for errors
2. Verify the basename logs match your deployment path
3. Ensure assets are loading (check Network tab)

### Issue: 404 on Page Refresh
**Cause**: Server not configured for SPA routing

**Solution**: 
1. Ensure `vercel.json` is deployed
2. For other hosts, configure server to serve `index.html` for all routes

### Issue: Assets Not Loading (404)
**Cause**: Incorrect base path in build

**Solution**:
1. Verify `VITE_BASE_PATH` environment variable was set during build
2. Check the built `dist/index.html` for correct asset paths
3. Rebuild with correct environment variable

## Environment Variables

| Variable | Purpose | Example |
|----------|---------|---------|
| `VITE_BASE_PATH` | Sets the base path for subpath deployment | `/infinite_banking/` |

## File Structure After Build

```
dist/
├── index.html          # Entry point with correct asset paths
├── assets/
│   ├── index-*.css    # Styles
│   └── index-*.js     # JavaScript bundle
└── ...                # Other static assets
```

For subpath deployment, all asset references in `index.html` will be prefixed with `/infinite_banking/`.
