import React from 'react';
const AboutJasonSection = () => {
  const wealthyUsers = ["<PERSON> maintains a $10M+ policy value as part of his banking strategy", "<PERSON> leverages policies for 'infinite returns' via his real estate empire", "<PERSON> holds policies worth over $10M for wealth protection", "<PERSON><PERSON><PERSON> partnered with Guardian Life for his financial planning strategy"];
  return <section className="section bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-vortex-dark font-montserrat">
            From Self-Made Millionaire to Financial Revolution Leader
          </h2>
        </div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div className="order-2 md:order-1">
            <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100">
              <p className="mb-4 text-gray-700 font-roboto">
                I've always had the entrepreneurial fire burning through my veins. Making money came naturally to me, and I've built multiple successful ventures throughout my life.
              </p>
              <p className="mb-4 text-gray-700 font-roboto">
                But there was a problem.
              </p>
              <p className="mb-4 text-gray-700 font-roboto">
                As great as I was at MAKING money, I was even better at SPENDING it. Like most entrepreneurs, I could generate cash flow, but building lasting wealth? That's where I struggled.
              </p>
              <p className="mb-4 text-gray-700 font-roboto">
                Then I discovered the enhanced version of the Infinite Banking Concept – what we now call Vortex Banking.
              </p>
              <p className="mb-4 text-gray-700 font-roboto">
                This wasn't just another financial strategy. This was a complete paradigm shift that changed EVERYTHING for my family and our financial future.
              </p>
              <p className="font-bold text-vortex-dark font-montserrat">
                Here's what makes Vortex Banking revolutionary: once you deposit money into this system, it NEVER leaves – it only compounds. Even after you SPEND this money, it still sits in your cash warehouse, earning interest and dividends until the day you die.
              </p>
            </div>
          </div>

          <div className="order-1 md:order-2">
            <div className="bg-gradient-to-br from-vortex to-vortex-tertiary text-white p-8 rounded-xl shadow-lg relative overflow-hidden">
              {/* Jason's Photo */}
              <div className="mb-6 flex justify-center">
                <div className="w-40 h-40 rounded-full overflow-hidden border-4 border-white/30 shadow-xl">
                  <img alt="Jason Hill" className="w-full h-full object-cover" src="/lovable-uploads/f2549734-0f13-49e0-836a-6d237f9fab33.png" />
                </div>
              </div>
            
              <h3 className="text-xl font-bold mb-4 font-montserrat text-center">Founder's Credibility</h3>
              <p className="mb-6 font-roboto">Jason Hill is the founder of LinkTech Foundation and an advocate of the Vortex Banking system. With over 30 years of experience as an entrepreneur and wealth strategist, Jason has helped thousands of families implement advanced financial strategies previously available only to the ultra-wealthy. His work has been recognized by industry leaders in banking, insurance, and wealth management.</p>

              <h3 className="text-xl font-bold mb-4 font-montserrat">Wealthy Individuals Using This Strategy:</h3>
              <ul className="space-y-3 font-roboto">
                {wealthyUsers.map((user, index) => <li key={index} className="flex items-start">
                    <span className="mr-3 text-vortex-light">•</span>
                    <span>{user}</span>
                  </li>)}
              </ul>
              
              {/* Decorative elements */}
              <div className="absolute -bottom-12 -right-12 w-40 h-40 bg-white/10 rounded-full blur-2xl"></div>
              <div className="absolute -top-12 -left-12 w-40 h-40 bg-white/10 rounded-full blur-2xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>;
};
export default AboutJasonSection;