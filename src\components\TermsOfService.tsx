
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

interface TermsOfServiceProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const TermsOfService = ({ open, onOpenChange }: TermsOfServiceProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] w-[90vw]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">Terms of Service</DialogTitle>
          <DialogDescription>
            Last Updated: May 12, 2025
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-6">
            <section>
              <h3 className="text-lg font-semibold mb-2">1. Acceptance of Terms</h3>
              <p>
                By accessing or using the services of Vortex Banker ("we", "our", or "us"), a brand owned by LinkTech Foundation, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using or accessing this site.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold mb-2">2. Use License</h3>
              <p>
                Permission is granted to temporarily access the materials on Vortex Banker's website for personal, non-commercial use only. This is the grant of a license, not a transfer of title, and under this license you may not:
              </p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Modify or copy the materials;</li>
                <li>Use the materials for any commercial purpose;</li>
                <li>Attempt to decompile or reverse engineer any software contained on Vortex Banker's website;</li>
                <li>Remove any copyright or other proprietary notations from the materials; or</li>
                <li>Transfer the materials to another person or "mirror" the materials on any other server.</li>
              </ul>
              <p className="mt-2">
                This license shall automatically terminate if you violate any of these restrictions and may be terminated by Vortex Banker at any time.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold mb-2">3. Disclaimer</h3>
              <p>
                The materials on Vortex Banker's website are provided on an 'as is' basis. Vortex Banker makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including, without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.
              </p>
              <p className="mt-2">
                Further, Vortex Banker does not warrant or make any representations concerning the accuracy, likely results, or reliability of the use of the materials on its website or otherwise relating to such materials or on any sites linked to this site.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold mb-2">4. Limitations</h3>
              <p>
                In no event shall Vortex Banker or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on Vortex Banker's website, even if Vortex Banker or a Vortex Banker authorized representative has been notified orally or in writing of the possibility of such damage.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold mb-2">5. Accuracy of Materials</h3>
              <p>
                The materials appearing on Vortex Banker's website could include technical, typographical, or photographic errors. Vortex Banker does not warrant that any of the materials on its website are accurate, complete, or current. Vortex Banker may make changes to the materials contained on its website at any time without notice. However, Vortex Banker does not make any commitment to update the materials.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold mb-2">6. Links</h3>
              <p>
                Vortex Banker has not reviewed all of the sites linked to its website and is not responsible for the contents of any such linked site. The inclusion of any link does not imply endorsement by Vortex Banker of the site. Use of any such linked website is at the user's own risk.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold mb-2">7. Modifications</h3>
              <p>
                Vortex Banker may revise these terms of service for its website at any time without notice. By using this website you are agreeing to be bound by the then current version of these terms of service.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold mb-2">8. Governing Law</h3>
              <p>
                These terms and conditions are governed by and construed in accordance with the laws and you irrevocably submit to the exclusive jurisdiction of the courts in that location.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold mb-2">9. Contact Information</h3>
              <p>
                If you have any questions about these Terms of Service, please contact us at:
              </p>
              <div className="mt-2">
                <p><strong>Vortex Banker</strong></p>
                <p>A brand of LinkTech Foundation</p>
                <p>Email: <EMAIL></p>
              </div>
            </section>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default TermsOfService;
