
import React from 'react';

const HowItWorksSection = () => {
  const strategies = [
    {
      title: "Contract Law Trust Strategy",
      description: "Using our proprietary legal structure, we leverage little-known contract law to override traditional IBC limitations, giving you unlimited tax-free growth potential that standard advisors simply don't understand."
    },
    {
      title: "MEC Optimization",
      description: "We strategically Modified Endowment Contract your policies from day one – the OPPOSITE of what traditional advisors recommend – then pair it with our trust strategy to maximize growth while maintaining tax advantages."
    },
    {
      title: "Contribution Flexibility",
      description: "Traditional IBC policies lock you into rigid contribution schedules. Miss a payment? Policy lapse risks. Get a $200K windfall? Sorry, can't add it. Vortex Banking gives you the flexibility to contribute the minimum while maintaining the ability to add up to $200K whenever opportunity knocks."
    },
    {
      title: "Generational Wealth Transfer",
      description: "When you pass away, your Vortex Banking policy pays out a MASSIVE tax-free death benefit to your Trust. The trustee then creates new policies for your beneficiaries, ensuring your wealth stays in the family for generations – completely outside the government's reach."
    }
  ];

  const historicalExamples = [
    "<PERSON> started Pampered Chef using a life insurance policy loan",
    "Foster Farms founders purchased their first farm using life insurance policy loans",
    "<PERSON> has discussed 'using insurance as a bank' for maintaining liquidity"
  ];

  return (
    <section className="section bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-vortex-dark">
            The Enhanced Infinite Banking Strategy Reserved For The 0.1% – Until Now
          </h2>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {strategies.map((strategy, index) => (
            <div 
              key={index}
              className="bg-white p-6 rounded-xl shadow-md border border-gray-100 animate-fade-in"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <span className="bg-vortex text-white text-sm font-medium px-2 py-1 rounded inline-block mb-3">
                Strategy {index + 1}
              </span>
              <h3 className="text-xl font-bold mb-3 text-vortex-dark">{strategy.title}</h3>
              <p className="text-gray-700">{strategy.description}</p>
            </div>
          ))}
        </div>

        <div className="bg-vortex-dark text-white p-8 rounded-xl">
          <h3 className="text-2xl font-bold mb-6">Historical Examples:</h3>
          <div className="grid md:grid-cols-3 gap-6">
            {historicalExamples.map((example, index) => (
              <div key={index} className="bg-white/10 p-5 rounded-lg">
                <p className="italic mb-3">"{example}"</p>
                <div className="flex items-center">
                  <span className="w-8 h-0.5 bg-vortex"></span>
                  <span className="ml-2 text-sm text-vortex-light">Historical Success</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
