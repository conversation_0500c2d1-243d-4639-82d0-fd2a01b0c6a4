
import React, { ReactNode } from 'react';

interface CTAButtonProps {
  text: ReactNode;
  onClick?: () => void;
  isPrimary?: boolean;
  className?: string;
}

const CTAButton = ({ 
  text, 
  onClick, 
  isPrimary = true, 
  className = '' 
}: CTAButtonProps) => {
  return (
    <button
      onClick={onClick}
      className={`${isPrimary 
        ? 'bg-gradient-to-r from-vortex to-vortex-secondary text-white hover:shadow-lg hover:shadow-vortex/20' 
        : 'bg-white text-vortex border border-vortex hover:bg-vortex/5'} 
        font-montserrat font-bold py-3 px-6 rounded-lg transition-all duration-300 hover:scale-[1.02] ${className}`}
    >
      {text}
    </button>
  );
};

export default CTAButton;
