import React from 'react';

const LogoItem = ({ name }: { name: string }) => (
  <div className="flex flex-col items-center justify-center p-4 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 transition-all duration-300 hover:bg-white/10">
    {/* Changed text-vortex to text-green-400 here */}
    <div className="font-playfair text-lg font-bold text-green-400">{name}</div>
  </div>
);

const SocialProofSection = () => {
  const logos = ["JPMorgan Chase", "Bank of America", "Wells Fargo", "Citigroup"];
  const celebrities = [
    "Walt Disney funded Disneyland when banks refused traditional financing",
    "<PERSON><PERSON><PERSON><PERSON> saved his company during the Great Depression using policy loans",
    "<PERSON> fueled <PERSON>'s early growth with strategic life insurance financing"
  ];

  return (
    <section className="py-12 bg-vortex-dark">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 opacity-80">
          {logos.map((logo, index) => (
            <LogoItem key={index} name={logo} />
          ))}
        </div>

        <div className="text-center text-white mb-12">
          <p className="text-lg md:text-xl opacity-90">
            The banking elite aren't just using this strategy – they're betting <span className="font-bold">BILLIONS</span> on it. Isn't it time you knew what they know?
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {celebrities.map((text, index) => (
            <div 
              key={index} 
              className="p-5 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 text-white"
            >
              <p className="italic">{text}</p>
              <div className="mt-3 flex items-center">
                <span className="inline-block w-6 h-0.5 bg-vortex"></span>
                <span className="ml-2 text-vortex text-sm font-medium">Success Story</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SocialProofSection;